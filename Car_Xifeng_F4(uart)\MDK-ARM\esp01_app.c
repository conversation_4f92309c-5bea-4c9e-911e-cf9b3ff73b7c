/**
 * ESP01 WiFi Module Application - Vehicle GPS Navigation System
 *
 * Main Functions:
 * 1. WiFi Connection Management - Connect to specified WiFi network
 * 2. Real-time GPS Upload - Upload GPS coordinates to ThingSpeak cloud
 * 3. Map Display - Show current position on web map
 * 4. Serial Command Control - Receive navigation commands via UART1
 * 5. Route Planning - Calculate and display navigation routes
 *
 * Author: AI Assistant
 * Version: 2.0
 * Date: 2025-01-25
 */

#include "esp01_app.h"
#include "GPS_app.h"
#include "navigation_app.h"
#include "stdlib.h"

extern uint8_t uart2_rx_dma_buffer[];
extern DMA_HandleTypeDef hdma_usart2_rx;

#define UART2_BUFFER_SIZE 512

// ==================== System State Management ====================
static ESP01_State_t esp01_state = ESP01_STATE_IDLE;
static uint32_t esp01_last_cmd_time = 0;
static uint8_t esp01_retry_count = 0;
static uint8_t esp01_init_step = 0;

// ==================== Network Connection Status ====================
static volatile uint8_t tcp_connected = 0;
static volatile uint8_t data_send_ready = 0;
static volatile uint8_t persistent_connection = 0;

// ==================== WiFi Configuration ====================
#define WIFI_SSID "Tenda_ZC_5G"
#define WIFI_PASSWORD "zhongchuang"
#define ESP01_MAX_RETRY 3
#define ESP01_CMD_TIMEOUT 5000

// ==================== ThingSpeak Cloud Service Configuration ====================
#define THINGSPEAK_API_KEY "LU22ZUP4ZTFK4IY9"
#define THINGSPEAK_HOST "api.thingspeak.com"
#define THINGSPEAK_CHANNEL "3014831"

// ==================== GPS Upload Interval ====================
#define GPS_UPLOAD_INTERVAL 15000  // Upload every 15 seconds

// ==================== Data Buffers ====================
static char http_request_buffer[400];
static char url_params_buffer[300];

/**
 * ESP01 Initialization Function
 * Set initial state and start WiFi connection process
 */
void esp01_Init()
{
    esp01_state = ESP01_STATE_INIT;
    esp01_last_cmd_time = HAL_GetTick();
    esp01_retry_count = 0;
    esp01_init_step = 0;
    tcp_connected = 0;
    data_send_ready = 0;
    persistent_connection = 0;

    my_printf(&huart1, "\r\n========== ESP01 WiFi System Start ==========\r\n");
    my_printf(&huart1, "WiFi Network: %s\r\n", WIFI_SSID);
    my_printf(&huart1, "Cloud Service: ThingSpeak (Channel %s)\r\n", THINGSPEAK_CHANNEL);
    my_printf(&huart1, "Map Service: Real-time GPS & Navigation\r\n");
    my_printf(&huart1, "==========================================\r\n\r\n");
}

/**
 * ESP01 Main Task Function
 * State machine for WiFi connection and data upload management
 */
void esp01_Task()
{
    uint32_t current_time = HAL_GetTick();

    switch(esp01_state)
    {
        case ESP01_STATE_IDLE:
            // Idle state, waiting for startup
            break;

        case ESP01_STATE_INIT:
            // Initialization state, execute WiFi connection sequence
            esp01_InitSequence();
            break;

        case ESP01_STATE_CONNECTING:
            // Connecting state, handle timeout and retry
            if(current_time - esp01_last_cmd_time > ESP01_CMD_TIMEOUT)
            {
                esp01_retry_count++;
                if(esp01_retry_count >= ESP01_MAX_RETRY)
                {
                    my_printf(&huart1, "ESP01: WiFi connection failed, resetting...\r\n");
                    esp01_state = ESP01_STATE_INIT;
                    esp01_init_step = 0;
                    esp01_retry_count = 0;
                }
                else
                {
                    my_printf(&huart1, "ESP01: Retry connection %d/%d\r\n", esp01_retry_count, ESP01_MAX_RETRY);
                    esp01_state = ESP01_STATE_INIT;
                    esp01_init_step = 0;
                }
                esp01_last_cmd_time = current_time;
            }
            break;

        case ESP01_STATE_CONNECTED:
            // Connected state, periodically upload GPS data
            {
                static uint32_t last_upload_time = 0;
                if(current_time - last_upload_time > GPS_UPLOAD_INTERVAL)
                {
                    my_printf(&huart1, "Starting GPS data upload...\r\n");
                    esp01_UploadGPSData();
                    last_upload_time = current_time;
                }

                // Periodically check connection status
                if(current_time - esp01_last_cmd_time > 300000) // Check every 5 minutes
                {
                    esp01_CheckConnection();
                    esp01_last_cmd_time = current_time;
                }
            }
            break;

        case ESP01_STATE_ERROR:
            // Error state, waiting for reset
            break;

        default:
            esp01_state = ESP01_STATE_IDLE;
            break;
    }
}

/**
 * WiFi Initialization Sequence
 * Execute AT commands step by step to connect WiFi
 */
void esp01_InitSequence()
{
    uint32_t current_time = HAL_GetTick();
    uint32_t wait_time = (esp01_init_step == 2) ? 5000 : 2000;

    if(current_time - esp01_last_cmd_time < wait_time)
    {
        return; // Wait for command execution to complete
    }

    switch(esp01_init_step)
    {
        case 0:
            my_printf(&huart1, "Step 1: Testing ESP01 communication...\r\n");
            Uart2_Printf(&huart2, "AT\r\n");
            esp01_init_step++;
            break;

        case 1:
            my_printf(&huart1, "Step 2: Restarting ESP01 module...\r\n");
            Uart2_Printf(&huart2, "AT+RST\r\n");
            esp01_init_step++;
            esp01_last_cmd_time = current_time;
            break;

        case 2:
            my_printf(&huart1, "Step 3: Setting WiFi mode...\r\n");
            HAL_UART_DMAStop(&huart2);
            HAL_UARTEx_ReceiveToIdle_DMA(&huart2, uart2_rx_dma_buffer, UART2_BUFFER_SIZE);
            __HAL_DMA_DISABLE_IT(&hdma_usart2_rx, DMA_IT_HT);
            Uart2_Printf(&huart2, "AT+CWMODE=1\r\n");
            esp01_init_step++;
            break;

        case 3:
            my_printf(&huart1, "Step 4: Connecting to WiFi [%s]...\r\n", WIFI_SSID);
            Uart2_Printf(&huart2, "AT+CWJAP=\"%s\",\"%s\"\r\n", WIFI_SSID, WIFI_PASSWORD);
            esp01_state = ESP01_STATE_CONNECTING;
            esp01_init_step = 0;
            break;

        default:
            esp01_init_step = 0;
            break;
    }
    esp01_last_cmd_time = current_time;
}

/**
 * Check WiFi Connection Status
 */
void esp01_CheckConnection()
{
    Uart2_Printf(&huart2, "AT+CWJAP?\r\n");
    my_printf(&huart1, "ESP01: Checking WiFi connection status\r\n");
}

/**
 * Get ESP01 Current State
 */
ESP01_State_t esp01_GetState()
{
    return esp01_state;
}

/**
 * Set WiFi Connection Success Status
 */
void esp01_SetConnected()
{
    if(esp01_state != ESP01_STATE_CONNECTED)
    {
        esp01_state = ESP01_STATE_CONNECTED;
        esp01_retry_count = 0;
        my_printf(&huart1, "ESP01: WiFi connected successfully!\r\n");
        my_printf(&huart1, "Network: %s\r\n", WIFI_SSID);
        my_printf(&huart1, "Ready for data transmission...\r\n");

        // After WiFi connection success, establish TCP connection
        HAL_Delay(2000);
        esp01_EstablishTCPConnection();
    }
}

/**
 * Set TCP Connection Success Status
 */
void esp01_SetTCPConnected()
{
    tcp_connected = 1;
    persistent_connection = 1;
    my_printf(&huart1, "TCP connection established successfully\r\n");
}

/**
 * Set Data Send Ready Status
 */
void esp01_SetDataSendReady()
{
    data_send_ready = 1;
    my_printf(&huart1, "ESP01: Ready to send data\r\n");
}

/**
 * Reset TCP Connection Status
 */
void esp01_ResetTCPState()
{
    tcp_connected = 0;
    data_send_ready = 0;
    persistent_connection = 0;
}

/**
 * Reset ESP01 Module
 */
void esp01_Reset()
{
    esp01_state = ESP01_STATE_INIT;
    esp01_last_cmd_time = HAL_GetTick();
    esp01_retry_count = 0;
    esp01_init_step = 0;
    esp01_ResetTCPState();
    my_printf(&huart1, "ESP01: Module reset\r\n");
}

/**
 * Send Custom AT Command
 */
void esp01_SendCommand(const char* command)
{
    Uart2_Printf(&huart2, "%s\r\n", command);
    my_printf(&huart1, "Send AT command: %s\r\n", command);
}

/**
 * Establish TCP Connection to ThingSpeak Server
 */
uint8_t esp01_EstablishTCPConnection()
{
    if(persistent_connection) {
        my_printf(&huart1, "TCP connection already exists, reusing...\r\n");
        return 1;
    }

    if(esp01_state != ESP01_STATE_CONNECTED) {
        my_printf(&huart1, "WiFi not connected, cannot establish TCP connection\r\n");
        return 0;
    }

    my_printf(&huart1, "Establishing TCP connection to %s...\r\n", THINGSPEAK_HOST);

    // Close any existing connection first
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(1000);

    // Establish new TCP connection
    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"%s\",80\r\n", THINGSPEAK_HOST);

    // Wait for connection establishment
    uint32_t timeout = HAL_GetTick() + 10000; // 10 second timeout
    while (!tcp_connected && HAL_GetTick() < timeout) {
        HAL_Delay(100);
    }

    if (tcp_connected) {
        persistent_connection = 1;
        my_printf(&huart1, "TCP connection established successfully!\r\n");
        return 1;
    } else {
        my_printf(&huart1, "TCP connection establishment failed\r\n");
        return 0;
    }
}

/**
 * Get Current GPS Location
 */
void esp01_GetRealLocation(float* lat, float* lon, float* alt)
{
    *lat = g_LatAndLongData.latitude;
    *lon = g_LatAndLongData.longitude;
    *alt = 50.0f;

    // If GPS data is invalid, use Hengyang Normal University as default location
    if (*lat == 0.0f || *lon == 0.0f) {
        *lat = 26.88693f;
        *lon = 112.675813f;
        *alt = 50.0f;
        my_printf(&huart1, "GPS data invalid, using default location\r\n");
    } else {
        my_printf(&huart1, "Using real GPS data: %.6fN, %.6fE\r\n", *lat, *lon);
    }
}

/**
 * Upload GPS Data to ThingSpeak
 */
void esp01_UploadGPSData()
{
    if(esp01_state != ESP01_STATE_CONNECTED) {
        my_printf(&huart1, "ESP01 not connected, cannot upload GPS data\r\n");
        return;
    }

    float lat, lon, alt;
    esp01_GetRealLocation(&lat, &lon, &alt);

    // Build URL parameters
    snprintf(url_params_buffer, sizeof(url_params_buffer),
             "/update?api_key=%s&field1=%.6f&field2=%.6f&field3=%.1f",
             THINGSPEAK_API_KEY, lat, lon, alt);

    // Build HTTP request (use keep-alive to maintain connection)
    snprintf(http_request_buffer, sizeof(http_request_buffer),
             "GET %s HTTP/1.1\r\n"
             "Host: %s\r\n"
             "Connection: keep-alive\r\n\r\n",
             url_params_buffer, THINGSPEAK_HOST);

    my_printf(&huart1, "Location: %.6fN, %.6fE, %.1fm\r\n", lat, lon, alt);
    my_printf(&huart1, "URL: %s%s\r\n", THINGSPEAK_HOST, url_params_buffer);

    // Ensure TCP connection exists
    if (!esp01_EstablishTCPConnection()) {
        my_printf(&huart1, "Cannot establish TCP connection\r\n");
        return;
    }

    // Reset data send status
    data_send_ready = 0;

    my_printf(&huart1, "Sending HTTP request (%d bytes)...\r\n", strlen(http_request_buffer));
    Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", strlen(http_request_buffer));

    // Wait for data send ready
    uint32_t timeout = HAL_GetTick() + 3000; // 3 second timeout
    while (!data_send_ready && HAL_GetTick() < timeout) {
        HAL_Delay(100);
    }

    if (!data_send_ready) {
        my_printf(&huart1, "Data send not ready!\r\n");
        return;
    }

    my_printf(&huart1, "Transmitting data...\r\n");
    Uart2_Printf(&huart2, "%s", http_request_buffer);

    my_printf(&huart1, "GPS data upload request sent!\r\n");
    if (lat == 26.88693f && lon == 112.675813f) {
        my_printf(&huart1, "Location: [DEFAULT] Hengyang Normal University %.6fN, %.6fE\r\n", lat, lon);
    } else {
        my_printf(&huart1, "Location: [REAL GPS] %.6fN, %.6fE\r\n", lat, lon);
    }
    my_printf(&huart1, "View map: https://687f369dfe095025e29a2697--relaxed-zabaione-3eabb4.netlify.app/\r\n");
    my_printf(&huart1, "Next upload in 15 seconds...\r\n\r\n");

    HAL_Delay(2000);
}

/**
 * Upload Navigation Data to ThingSpeak
 * Include current position, target position, distance and direction information
 */
void esp01_SendNavigationData()
{
    if(esp01_state != ESP01_STATE_CONNECTED) {
        my_printf(&huart1, "ESP01 not connected, cannot send navigation data\r\n");
        return;
    }

    // Get current location
    float current_lat, current_lon, current_alt;
    esp01_GetRealLocation(&current_lat, &current_lon, &current_alt);

    // Get target location
    float dest_lat, dest_lon;
    if (current_navigation.is_active) {
        dest_lat = current_navigation.destination.latitude;
        dest_lon = current_navigation.destination.longitude;
        my_printf(&huart1, "Navigation target: %s\r\n", current_navigation.destination.description);
    } else {
        // Default target: Wangda Plaza
        dest_lat = 26.8869f;
        dest_lon = 112.6758f;
        my_printf(&huart1, "Default target: Wangda Plaza\r\n");
    }

    // Calculate distance and direction
    float distance = Navigation_CalculateDistance(current_lat, current_lon, dest_lat, dest_lon);
    float bearing = Navigation_CalculateBearing(current_lat, current_lon, dest_lat, dest_lon);

    // Build navigation data URL (use field5=1 to identify navigation data)
    snprintf(url_params_buffer, sizeof(url_params_buffer),
             "/update?api_key=%s&field1=%.6f&field2=%.6f&field3=%.6f&field4=%.6f&field5=1",
             THINGSPEAK_API_KEY, current_lat, current_lon, dest_lat, dest_lon);

    // Build HTTP request
    snprintf(http_request_buffer, sizeof(http_request_buffer),
             "GET %s HTTP/1.1\r\n"
             "Host: %s\r\n"
             "Connection: keep-alive\r\n\r\n",
             url_params_buffer, THINGSPEAK_HOST);

    my_printf(&huart1, "========== Navigation Data Upload ==========\r\n");
    my_printf(&huart1, "Current position: %.6fN, %.6fE\r\n", current_lat, current_lon);
    my_printf(&huart1, "Target position: %.6fN, %.6fE\r\n", dest_lat, dest_lon);
    my_printf(&huart1, "Distance: %.0f meters\r\n", distance);
    my_printf(&huart1, "Bearing: %.0f degrees\r\n", bearing);
    my_printf(&huart1, "ThingSpeak Channel: %s\r\n", THINGSPEAK_CHANNEL);

    // Ensure TCP connection exists
    if (!esp01_EstablishTCPConnection()) {
        my_printf(&huart1, "Cannot establish TCP connection\r\n");
        return;
    }

    // Reset data send status
    data_send_ready = 0;

    my_printf(&huart1, "Sending navigation HTTP request (%d bytes)...\r\n", strlen(http_request_buffer));
    Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", strlen(http_request_buffer));

    // Wait for data send ready
    uint32_t timeout = HAL_GetTick() + 3000; // 3 second timeout
    while (!data_send_ready && HAL_GetTick() < timeout) {
        HAL_Delay(100);
    }

    if (!data_send_ready) {
        my_printf(&huart1, "Data send not ready!\r\n");
        return;
    }

    my_printf(&huart1, "Transmitting navigation data...\r\n");
    Uart2_Printf(&huart2, "%s", http_request_buffer);

    my_printf(&huart1, "Navigation data upload successful!\r\n");
    my_printf(&huart1, "Map will display navigation route\r\n");
    my_printf(&huart1, "Connection kept open (keep-alive)\r\n");
    my_printf(&huart1, "=====================================\r\n\r\n");

    HAL_Delay(2000);
}

/**
 * Start Initialization Sequence
 */
void esp01_StartInit()
{
    if(esp01_state == ESP01_STATE_IDLE)
    {
        esp01_state = ESP01_STATE_INIT;
        esp01_last_cmd_time = HAL_GetTick();
        esp01_retry_count = 0;
        esp01_init_step = 0;
        my_printf(&huart1, "Starting ESP01 initialization sequence\r\n");
    }
}